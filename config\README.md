# 游戏配置文件说明

本目录包含游戏的各种配置文件，支持多种格式：

## 配置文件优先级

1. **Proto 二进制文件** (`game_config.pb`) - 最高优先级，性能最佳
2. **Excel 文件** (`game_config.xlsx`) - 中等优先级，便于编辑
3. **JSON 文件** (`../config.json`) - 最低优先级，兼容性配置

## Excel 配置文件结构

### Database 工作表
| 键名 | 值 | 说明 |
|------|----|----|
| uri | mongodb://*************:27017 | 数据库连接URI |
| db_name | tryGoAndMongo | 数据库名称 |
| connection_timeout | 10 | 连接超时时间(秒) |
| max_pool_size | 100 | 最大连接池大小 |
| min_pool_size | 10 | 最小连接池大小 |

### Server 工作表
| 键名 | 值 | 说明 |
|------|----|----|
| host | localhost | 服务器主机地址 |
| port | 8080 | 服务器端口 |
| debug_mode | true | 调试模式 |
| max_connections | 1000 | 最大连接数 |
| read_timeout | 30 | 读取超时时间(秒) |
| write_timeout | 30 | 写入超时时间(秒) |

### Game 工作表
| 键名 | 值 | 说明 |
|------|----|----|
| game_name | CopyTheGame | 游戏名称 |
| version | 1.0.0 | 游戏版本 |
| max_players | 1000 | 最大玩家数 |
| level_cap | 100 | 等级上限 |
| exp_multiplier | 1.5 | 经验倍率 |
| gold_multiplier | 2 | 金币倍率 |

### Characters 工作表
| ID | Name | Description | BaseHP | BaseMP | BaseAttack | BaseDefense | BaseSpeed | SkillIDs |
|----|------|-------------|--------|--------|------------|-------------|-----------|----------|
| 1 | 战士 | 近战物理职业 | 100 | 20 | 25 | 20 | 15 | 1,2,3 |
| 2 | 法师 | 远程魔法职业 | 60 | 100 | 30 | 10 | 20 | 4,5,6 |
| 3 | 弓手 | 远程物理职业 | 80 | 40 | 28 | 15 | 25 | 7,8,9 |

## 使用方法

1. **编辑配置**: 修改 `game_config.xlsx` 文件
2. **生成二进制**: 运行配置转换工具生成 `game_config.pb`
3. **重启服务**: 重启游戏服务器以加载新配置

## 配置转换工具

```go
// 从 Excel 生成 Proto 二进制文件
excelMaker := config.NewExcelMaker("./config/game_config.xlsx")
gameConfig, err := excelMaker.LoadGameConfiguration()
if err != nil {
    log.Fatal(err)
}
err = excelMaker.SaveToProtoFile(gameConfig, "./config/game_config.pb")
if err != nil {
    log.Fatal(err)
}
```

## 注意事项

- Excel 文件必须严格按照上述格式创建
- 工作表名称必须准确匹配（区分大小写）
- 数值类型字段必须是有效的数字
- 布尔类型字段使用 "true" 或 "false"
- 数组字段使用逗号分隔（如 SkillIDs: "1,2,3"）
