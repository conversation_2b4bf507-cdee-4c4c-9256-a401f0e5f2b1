{"database": {"uri": "mongodb://*************:27017", "db_name": "tryGoAndMongo", "connection_timeout": 10, "max_pool_size": 100, "min_pool_size": 10}, "server": {"host": "localhost", "port": 8080, "debug_mode": true, "max_connections": 1000, "read_timeout": 30, "write_timeout": 30}, "game": {"game_name": "CopyTheGame", "version": "1.0.0", "max_players": 1000, "level_cap": 100, "exp_multiplier": 1.5, "gold_multiplier": 2}, "characters": [{"character_id": 1, "name": "战士", "description": "近战物理职业，拥有强大的攻击力和防御力", "base_hp": 100, "base_mp": 20, "base_attack": 25, "base_defense": 20, "base_speed": 15, "skill_ids": [1, 2, 3]}, {"character_id": 2, "name": "法师", "description": "远程魔法职业，拥有强大的魔法攻击力", "base_hp": 60, "base_mp": 100, "base_attack": 30, "base_defense": 10, "base_speed": 20, "skill_ids": [4, 5, 6]}, {"character_id": 3, "name": "弓手", "description": "远程物理职业，拥有高速度和精准攻击", "base_hp": 80, "base_mp": 40, "base_attack": 28, "base_defense": 15, "base_speed": 25, "skill_ids": [7, 8, 9]}, {"character_id": 4, "name": "盗贼", "description": "敏捷暗杀职业，拥有极高的速度和暴击率", "base_hp": 70, "base_mp": 30, "base_attack": 22, "base_defense": 12, "base_speed": 30, "skill_ids": [10, 11, 12]}, {"character_id": 5, "name": "牧师", "description": "治疗辅助职业，拥有强大的治疗和辅助能力", "base_hp": 90, "base_mp": 80, "base_attack": 15, "base_defense": 18, "base_speed": 18, "skill_ids": [13, 14, 15]}]}