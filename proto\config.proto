syntax = "proto3";

package config;

option go_package = "copyTheGame/proto/config";

// 数据库配置
message DatabaseConfig {
  string uri = 1;
  string db_name = 2;
  int32 connection_timeout = 3;
  int32 max_pool_size = 4;
  int32 min_pool_size = 5;
}

// 服务器配置
message ServerConfig {
  string host = 1;
  int32 port = 2;
  bool debug_mode = 3;
  int32 max_connections = 4;
  int32 read_timeout = 5;
  int32 write_timeout = 6;
}

// 游戏基础配置
message GameConfig {
  string game_name = 1;
  string version = 2;
  int32 max_players = 3;
  int32 level_cap = 4;
  double exp_multiplier = 5;
  int32 gold_multiplier = 6;
}

// 角色配置
message CharacterConfig {
  int32 character_id = 1;
  string name = 2;
  string description = 3;
  int32 base_hp = 4;
  int32 base_mp = 5;
  int32 base_attack = 6;
  int32 base_defense = 7;
  int32 base_speed = 8;
  repeated int32 skill_ids = 9;
}

// 技能配置
message SkillConfig {
  int32 skill_id = 1;
  string name = 2;
  string description = 3;
  int32 damage = 4;
  int32 mp_cost = 5;
  int32 cooldown = 6;
  string skill_type = 7;
  repeated string effects = 8;
}

// 物品配置
message ItemConfig {
  int32 item_id = 1;
  string name = 2;
  string description = 3;
  string item_type = 4;
  int32 value = 5;
  int32 stack_size = 6;
  bool tradeable = 7;
  map<string, int32> attributes = 8;
}

// 关卡配置
message LevelConfig {
  int32 level_id = 1;
  string name = 2;
  string description = 3;
  int32 required_level = 4;
  repeated int32 enemy_ids = 5;
  repeated int32 reward_item_ids = 6;
  int32 exp_reward = 7;
  int32 gold_reward = 8;
}

// 敌人配置
message EnemyConfig {
  int32 enemy_id = 1;
  string name = 2;
  string description = 3;
  int32 level = 4;
  int32 hp = 5;
  int32 mp = 6;
  int32 attack = 7;
  int32 defense = 8;
  int32 speed = 9;
  repeated int32 skill_ids = 10;
  repeated int32 drop_item_ids = 11;
  repeated double drop_rates = 12;
}

// 主配置文件，包含所有配置
message GameConfiguration {
  DatabaseConfig database = 1;
  ServerConfig server = 2;
  GameConfig game = 3;
  repeated CharacterConfig characters = 4;
  repeated SkillConfig skills = 5;
  repeated ItemConfig items = 6;
  repeated LevelConfig levels = 7;
  repeated EnemyConfig enemies = 8;
}
