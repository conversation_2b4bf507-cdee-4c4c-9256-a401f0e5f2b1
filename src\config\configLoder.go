package config

import (
	"encoding/json"
	"log"
	"os"
)

// 配置管理器（简化版，使用传统 JSON 配置）
type ConfigManager struct {
	jsonConfig map[string]string
}

var globalConfigManager *ConfigManager

func init() {
	globalConfigManager = NewConfigManager()
	globalConfigManager.loadJSONConfig()
}

// NewConfigManager 创建新的配置管理器
func NewConfigManager() *ConfigManager {
	return &ConfigManager{
		jsonConfig: make(map[string]string),
	}
}

// loadJSONConfig 加载传统的 JSON 配置
func (cm *ConfigManager) loadJSONConfig() {
	data, err := os.ReadFile("./config.json")
	if err != nil {
		log.Printf("Warning: Failed to load config.json, using default values: %v", err)
		// 设置默认值
		cm.jsonConfig["uri"] = "mongodb://192.168.0.164:27017"
		cm.jsonConfig["dbName"] = "tryGoAndMongo"
		return
	}
	err = json.Unmarshal(data, &cm.jsonConfig)
	if err != nil {
		log.Fatal("Failed to parse JSON config:", err)
	}
	log.Println("Loaded configuration from JSON file")
}

// GetConfig 获取配置值（兼容旧接口）
func GetConfig(key string) string {
	return globalConfigManager.GetConfigValue(key)
}

// GetConfigValue 获取配置值
func (cm *ConfigManager) GetConfigValue(key string) string {
	if val, exists := cm.jsonConfig[key]; exists {
		return val
	}
	return ""
}

// 为了兼容性，保留这些函数但返回 nil
func GetGameConfiguration() interface{} {
	return nil
}

func GetDatabaseConfig() interface{} {
	return nil
}

func GetServerConfig() interface{} {
	return nil
}

func GetGameBasicConfig() interface{} {
	return nil
}

func GetCharacterConfigs() interface{} {
	return nil
}

func GetCharacterConfigByID(characterID int32) interface{} {
	return nil
}
