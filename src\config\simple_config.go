package config

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/tealeg/xlsx/v3"
)

// 简化的配置结构，不依赖 Proto
type SimpleGameConfig struct {
	Database   *SimpleDatabaseConfig   `json:"database"`
	Server     *SimpleServerConfig     `json:"server"`
	Game       *SimpleGameBasicConfig  `json:"game"`
	Characters []*SimpleCharacterConfig `json:"characters"`
}

type SimpleDatabaseConfig struct {
	URI               string `json:"uri"`
	DBName            string `json:"db_name"`
	ConnectionTimeout int32  `json:"connection_timeout"`
	MaxPoolSize       int32  `json:"max_pool_size"`
	MinPoolSize       int32  `json:"min_pool_size"`
}

type SimpleServerConfig struct {
	Host           string `json:"host"`
	Port           int32  `json:"port"`
	DebugMode      bool   `json:"debug_mode"`
	MaxConnections int32  `json:"max_connections"`
	ReadTimeout    int32  `json:"read_timeout"`
	WriteTimeout   int32  `json:"write_timeout"`
}

type SimpleGameBasicConfig struct {
	GameName       string  `json:"game_name"`
	Version        string  `json:"version"`
	MaxPlayers     int32   `json:"max_players"`
	LevelCap       int32   `json:"level_cap"`
	ExpMultiplier  float64 `json:"exp_multiplier"`
	GoldMultiplier int32   `json:"gold_multiplier"`
}

type SimpleCharacterConfig struct {
	CharacterID int32   `json:"character_id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	BaseHP      int32   `json:"base_hp"`
	BaseMP      int32   `json:"base_mp"`
	BaseAttack  int32   `json:"base_attack"`
	BaseDefense int32   `json:"base_defense"`
	BaseSpeed   int32   `json:"base_speed"`
	SkillIDs    []int32 `json:"skill_ids"`
}

// 简化的配置管理器
type SimpleConfigManager struct {
	gameConfig *SimpleGameConfig
	jsonConfig map[string]string
}

var simpleGlobalConfig *SimpleConfigManager

func init() {
	simpleGlobalConfig = NewSimpleConfigManager()
	if err := simpleGlobalConfig.LoadConfig(); err != nil {
		log.Printf("Warning: Failed to load advanced config, using defaults: %v", err)
		// 设置默认配置而不是尝试加载文件
		simpleGlobalConfig.setDefaultConfig()
	}
}

// NewSimpleConfigManager 创建简化配置管理器
func NewSimpleConfigManager() *SimpleConfigManager {
	return &SimpleConfigManager{
		jsonConfig: make(map[string]string),
	}
}

// LoadConfig 加载配置
func (scm *SimpleConfigManager) LoadConfig() error {
	// 1. 尝试从 JSON 配置文件加载
	if err := scm.loadFromJSONFile("./config/game_config.json"); err == nil {
		log.Println("Loaded configuration from JSON config file")
		return nil
	}

	// 2. 尝试从 Excel 文件加载
	if err := scm.loadFromExcelFile("./config/game_config.xlsx"); err == nil {
		log.Println("Loaded configuration from Excel file")
		return nil
	}

	return fmt.Errorf("failed to load configuration from any source")
}

// loadFromJSONFile 从 JSON 文件加载配置
func (scm *SimpleConfigManager) loadFromJSONFile(filePath string) error {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return err
	}

	scm.gameConfig = &SimpleGameConfig{}
	return json.Unmarshal(data, scm.gameConfig)
}

// loadFromExcelFile 从 Excel 文件加载配置
func (scm *SimpleConfigManager) loadFromExcelFile(filePath string) error {
	xlFile, err := xlsx.OpenFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to open Excel file: %v", err)
	}

	scm.gameConfig = &SimpleGameConfig{}

	// 加载数据库配置
	if dbConfig, err := scm.loadDatabaseConfigFromExcel(xlFile); err == nil {
		scm.gameConfig.Database = dbConfig
	}

	// 加载服务器配置
	if serverConfig, err := scm.loadServerConfigFromExcel(xlFile); err == nil {
		scm.gameConfig.Server = serverConfig
	}

	// 加载游戏配置
	if gameConfig, err := scm.loadGameConfigFromExcel(xlFile); err == nil {
		scm.gameConfig.Game = gameConfig
	}

	// 加载角色配置
	if characters, err := scm.loadCharacterConfigsFromExcel(xlFile); err == nil {
		scm.gameConfig.Characters = characters
	}

	return nil
}

// loadDatabaseConfigFromExcel 从 Excel 加载数据库配置
func (scm *SimpleConfigManager) loadDatabaseConfigFromExcel(xlFile *xlsx.File) (*SimpleDatabaseConfig, error) {
	sheet, ok := xlFile.Sheet["Database"]
	if !ok {
		return nil, fmt.Errorf("Database sheet not found")
	}

	dbConfig := &SimpleDatabaseConfig{}
	
	err := sheet.ForEachRow(func(r *xlsx.Row) error {
		key := r.GetCell(0).String()
		value := r.GetCell(1).String()
		
		switch key {
		case "uri":
			dbConfig.URI = value
		case "db_name":
			dbConfig.DBName = value
		case "connection_timeout":
			if val, err := strconv.Atoi(value); err == nil {
				dbConfig.ConnectionTimeout = int32(val)
			}
		case "max_pool_size":
			if val, err := strconv.Atoi(value); err == nil {
				dbConfig.MaxPoolSize = int32(val)
			}
		case "min_pool_size":
			if val, err := strconv.Atoi(value); err == nil {
				dbConfig.MinPoolSize = int32(val)
			}
		}
		return nil
	})

	return dbConfig, err
}

// loadServerConfigFromExcel 从 Excel 加载服务器配置
func (scm *SimpleConfigManager) loadServerConfigFromExcel(xlFile *xlsx.File) (*SimpleServerConfig, error) {
	sheet, ok := xlFile.Sheet["Server"]
	if !ok {
		return nil, fmt.Errorf("Server sheet not found")
	}

	serverConfig := &SimpleServerConfig{}
	
	err := sheet.ForEachRow(func(r *xlsx.Row) error {
		key := r.GetCell(0).String()
		value := r.GetCell(1).String()
		
		switch key {
		case "host":
			serverConfig.Host = value
		case "port":
			if val, err := strconv.Atoi(value); err == nil {
				serverConfig.Port = int32(val)
			}
		case "debug_mode":
			serverConfig.DebugMode = strings.ToLower(value) == "true"
		case "max_connections":
			if val, err := strconv.Atoi(value); err == nil {
				serverConfig.MaxConnections = int32(val)
			}
		case "read_timeout":
			if val, err := strconv.Atoi(value); err == nil {
				serverConfig.ReadTimeout = int32(val)
			}
		case "write_timeout":
			if val, err := strconv.Atoi(value); err == nil {
				serverConfig.WriteTimeout = int32(val)
			}
		}
		return nil
	})

	return serverConfig, err
}

// loadGameConfigFromExcel 从 Excel 加载游戏配置
func (scm *SimpleConfigManager) loadGameConfigFromExcel(xlFile *xlsx.File) (*SimpleGameBasicConfig, error) {
	sheet, ok := xlFile.Sheet["Game"]
	if !ok {
		return nil, fmt.Errorf("Game sheet not found")
	}

	gameConfig := &SimpleGameBasicConfig{}
	
	err := sheet.ForEachRow(func(r *xlsx.Row) error {
		key := r.GetCell(0).String()
		value := r.GetCell(1).String()
		
		switch key {
		case "game_name":
			gameConfig.GameName = value
		case "version":
			gameConfig.Version = value
		case "max_players":
			if val, err := strconv.Atoi(value); err == nil {
				gameConfig.MaxPlayers = int32(val)
			}
		case "level_cap":
			if val, err := strconv.Atoi(value); err == nil {
				gameConfig.LevelCap = int32(val)
			}
		case "exp_multiplier":
			if val, err := strconv.ParseFloat(value, 64); err == nil {
				gameConfig.ExpMultiplier = val
			}
		case "gold_multiplier":
			if val, err := strconv.Atoi(value); err == nil {
				gameConfig.GoldMultiplier = int32(val)
			}
		}
		return nil
	})

	return gameConfig, err
}

// loadCharacterConfigsFromExcel 从 Excel 加载角色配置
func (scm *SimpleConfigManager) loadCharacterConfigsFromExcel(xlFile *xlsx.File) ([]*SimpleCharacterConfig, error) {
	sheet, ok := xlFile.Sheet["Characters"]
	if !ok {
		return nil, fmt.Errorf("Characters sheet not found")
	}

	var characters []*SimpleCharacterConfig
	
	rowIndex := 0
	err := sheet.ForEachRow(func(r *xlsx.Row) error {
		if rowIndex == 0 {
			rowIndex++
			return nil // 跳过标题行
		}
		
		character := &SimpleCharacterConfig{}
		
		if val, err := strconv.Atoi(r.GetCell(0).String()); err == nil {
			character.CharacterID = int32(val)
		}
		character.Name = r.GetCell(1).String()
		character.Description = r.GetCell(2).String()
		
		if val, err := strconv.Atoi(r.GetCell(3).String()); err == nil {
			character.BaseHP = int32(val)
		}
		if val, err := strconv.Atoi(r.GetCell(4).String()); err == nil {
			character.BaseMP = int32(val)
		}
		if val, err := strconv.Atoi(r.GetCell(5).String()); err == nil {
			character.BaseAttack = int32(val)
		}
		if val, err := strconv.Atoi(r.GetCell(6).String()); err == nil {
			character.BaseDefense = int32(val)
		}
		if val, err := strconv.Atoi(r.GetCell(7).String()); err == nil {
			character.BaseSpeed = int32(val)
		}
		
		// 解析技能ID列表
		skillIDsStr := r.GetCell(8).String()
		if skillIDsStr != "" {
			skillIDStrs := strings.Split(skillIDsStr, ",")
			for _, idStr := range skillIDStrs {
				if id, err := strconv.Atoi(strings.TrimSpace(idStr)); err == nil {
					character.SkillIDs = append(character.SkillIDs, int32(id))
				}
			}
		}
		
		characters = append(characters, character)
		rowIndex++
		return nil
	})

	return characters, err
}

// loadJSONConfig 加载传统 JSON 配置
func (scm *SimpleConfigManager) loadJSONConfig() {
	data, err := os.ReadFile("./config.json")
	if err != nil {
		log.Fatal("Failed to load any configuration:", err)
	}
	err = json.Unmarshal(data, &scm.jsonConfig)
	if err != nil {
		log.Fatal("Failed to parse JSON config:", err)
	}
	log.Println("Loaded configuration from JSON file")
}

// GetSimpleGameConfig 获取简化游戏配置
func GetSimpleGameConfig() *SimpleGameConfig {
	return simpleGlobalConfig.gameConfig
}

// GetSimpleDatabaseConfig 获取简化数据库配置
func GetSimpleDatabaseConfig() *SimpleDatabaseConfig {
	if simpleGlobalConfig.gameConfig != nil {
		return simpleGlobalConfig.gameConfig.Database
	}
	return nil
}

// GetSimpleServerConfig 获取简化服务器配置
func GetSimpleServerConfig() *SimpleServerConfig {
	if simpleGlobalConfig.gameConfig != nil {
		return simpleGlobalConfig.gameConfig.Server
	}
	return nil
}

// GetSimpleCharacterConfigs 获取简化角色配置
func GetSimpleCharacterConfigs() []*SimpleCharacterConfig {
	if simpleGlobalConfig.gameConfig != nil {
		return simpleGlobalConfig.gameConfig.Characters
	}
	return nil
}
