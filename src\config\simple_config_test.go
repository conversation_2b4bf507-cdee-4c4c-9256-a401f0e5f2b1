package config

import (
	"encoding/json"
	"os"
	"testing"
)

func TestSimpleConfigManager(t *testing.T) {
	// 测试简化配置管理器创建
	scm := NewSimpleConfigManager()
	if scm == nil {
		t.Fatal("Failed to create SimpleConfigManager")
	}

	// 创建临时测试配置文件
	testConfig := &SimpleGameConfig{
		Database: &SimpleDatabaseConfig{
			URI:               "mongodb://localhost:27017",
			DBName:            "testdb",
			ConnectionTimeout: 10,
			MaxPoolSize:       50,
			MinPoolSize:       5,
		},
		Server: &SimpleServerConfig{
			Host:           "localhost",
			Port:           8080,
			DebugMode:      true,
			MaxConnections: 1000,
			ReadTimeout:    30,
			WriteTimeout:   30,
		},
		Game: &SimpleGameBasicConfig{
			GameName:       "TestGame",
			Version:        "1.0.0",
			MaxPlayers:     100,
			LevelCap:       50,
			ExpMultiplier:  1.5,
			GoldMultiplier: 2,
		},
		Characters: []*SimpleCharacterConfig{
			{
				CharacterID: 1,
				Name:        "TestWarrior",
				Description: "A test warrior",
				BaseHP:      100,
				BaseMP:      20,
				BaseAttack:  25,
				BaseDefense: 20,
				BaseSpeed:   15,
				SkillIDs:    []int32{1, 2, 3},
			},
		},
	}

	// 将测试配置写入临时文件
	data, err := json.MarshalIndent(testConfig, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal test config: %v", err)
	}

	tempFile := "./test_config.json"
	err = os.WriteFile(tempFile, data, 0644)
	if err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}
	defer os.Remove(tempFile) // 清理临时文件

	// 测试从 JSON 文件加载
	err = scm.loadFromJSONFile(tempFile)
	if err != nil {
		t.Fatalf("Failed to load from JSON file: %v", err)
	}

	// 验证配置内容
	if scm.gameConfig.Database == nil {
		t.Error("Database config should not be nil")
	} else {
		if scm.gameConfig.Database.URI != "mongodb://localhost:27017" {
			t.Errorf("Expected URI 'mongodb://localhost:27017', got '%s'", scm.gameConfig.Database.URI)
		}
		if scm.gameConfig.Database.DBName != "testdb" {
			t.Errorf("Expected DB name 'testdb', got '%s'", scm.gameConfig.Database.DBName)
		}
	}

	if scm.gameConfig.Server == nil {
		t.Error("Server config should not be nil")
	} else {
		if scm.gameConfig.Server.Port != 8080 {
			t.Errorf("Expected port 8080, got %d", scm.gameConfig.Server.Port)
		}
		if !scm.gameConfig.Server.DebugMode {
			t.Error("Expected debug mode to be true")
		}
	}

	if scm.gameConfig.Game == nil {
		t.Error("Game config should not be nil")
	} else {
		if scm.gameConfig.Game.GameName != "TestGame" {
			t.Errorf("Expected game name 'TestGame', got '%s'", scm.gameConfig.Game.GameName)
		}
		if scm.gameConfig.Game.ExpMultiplier != 1.5 {
			t.Errorf("Expected exp multiplier 1.5, got %f", scm.gameConfig.Game.ExpMultiplier)
		}
	}

	if len(scm.gameConfig.Characters) != 1 {
		t.Errorf("Expected 1 character, got %d", len(scm.gameConfig.Characters))
	} else {
		char := scm.gameConfig.Characters[0]
		if char.Name != "TestWarrior" {
			t.Errorf("Expected character name 'TestWarrior', got '%s'", char.Name)
		}
		if len(char.SkillIDs) != 3 {
			t.Errorf("Expected 3 skill IDs, got %d", len(char.SkillIDs))
		}
	}

	t.Log("Simple config manager test passed")
}

func TestSimpleConfigStructures(t *testing.T) {
	// 测试数据库配置结构
	dbConfig := &SimpleDatabaseConfig{
		URI:               "mongodb://localhost:27017",
		DBName:            "testdb",
		ConnectionTimeout: 10,
		MaxPoolSize:       50,
		MinPoolSize:       5,
	}

	if dbConfig.URI != "mongodb://localhost:27017" {
		t.Error("Database URI field failed")
	}
	if dbConfig.DBName != "testdb" {
		t.Error("Database name field failed")
	}
	if dbConfig.ConnectionTimeout != 10 {
		t.Error("Connection timeout field failed")
	}

	// 测试服务器配置结构
	serverConfig := &SimpleServerConfig{
		Host:           "localhost",
		Port:           8080,
		DebugMode:      true,
		MaxConnections: 1000,
		ReadTimeout:    30,
		WriteTimeout:   30,
	}

	if serverConfig.Host != "localhost" {
		t.Error("Server host field failed")
	}
	if serverConfig.Port != 8080 {
		t.Error("Server port field failed")
	}
	if !serverConfig.DebugMode {
		t.Error("Server debug mode field failed")
	}

	// 测试游戏配置结构
	gameConfig := &SimpleGameBasicConfig{
		GameName:       "TestGame",
		Version:        "1.0.0",
		MaxPlayers:     100,
		LevelCap:       50,
		ExpMultiplier:  1.5,
		GoldMultiplier: 2,
	}

	if gameConfig.GameName != "TestGame" {
		t.Error("Game name field failed")
	}
	if gameConfig.Version != "1.0.0" {
		t.Error("Game version field failed")
	}
	if gameConfig.ExpMultiplier != 1.5 {
		t.Error("Exp multiplier field failed")
	}

	// 测试角色配置结构
	characterConfig := &SimpleCharacterConfig{
		CharacterID: 1,
		Name:        "TestWarrior",
		Description: "A test warrior character",
		BaseHP:      100,
		BaseMP:      20,
		BaseAttack:  25,
		BaseDefense: 20,
		BaseSpeed:   15,
		SkillIDs:    []int32{1, 2, 3},
	}

	if characterConfig.CharacterID != 1 {
		t.Error("Character ID field failed")
	}
	if characterConfig.Name != "TestWarrior" {
		t.Error("Character name field failed")
	}
	if len(characterConfig.SkillIDs) != 3 {
		t.Error("Character skill IDs field failed")
	}

	// 测试完整配置结构
	fullConfig := &SimpleGameConfig{
		Database:   dbConfig,
		Server:     serverConfig,
		Game:       gameConfig,
		Characters: []*SimpleCharacterConfig{characterConfig},
	}

	if fullConfig.Database == nil {
		t.Error("Full config database field failed")
	}
	if fullConfig.Server == nil {
		t.Error("Full config server field failed")
	}
	if fullConfig.Game == nil {
		t.Error("Full config game field failed")
	}
	if len(fullConfig.Characters) != 1 {
		t.Error("Full config characters field failed")
	}

	t.Log("All simple config structure tests passed")
}

func TestGlobalSimpleConfigFunctions(t *testing.T) {
	// 测试全局简化配置函数
	gameConfig := GetSimpleGameConfig()
	if gameConfig != nil {
		t.Log("Successfully retrieved simple game configuration")
		
		if gameConfig.Database != nil {
			t.Logf("Database config: URI=%s, Name=%s", gameConfig.Database.URI, gameConfig.Database.DBName)
		}
		
		if gameConfig.Server != nil {
			t.Logf("Server config: Host=%s, Port=%d", gameConfig.Server.Host, gameConfig.Server.Port)
		}
		
		if gameConfig.Game != nil {
			t.Logf("Game config: Name=%s, Version=%s", gameConfig.Game.GameName, gameConfig.Game.Version)
		}
		
		if len(gameConfig.Characters) > 0 {
			t.Logf("Found %d character configs", len(gameConfig.Characters))
		}
	} else {
		t.Log("Simple game configuration is nil (expected if no config file loaded)")
	}

	// 测试获取特定配置
	dbConfig := GetSimpleDatabaseConfig()
	if dbConfig != nil {
		t.Logf("Simple database config: URI=%s", dbConfig.URI)
	} else {
		t.Log("Simple database config is nil")
	}

	serverConfig := GetSimpleServerConfig()
	if serverConfig != nil {
		t.Logf("Simple server config: Host=%s, Port=%d", serverConfig.Host, serverConfig.Port)
	} else {
		t.Log("Simple server config is nil")
	}

	characters := GetSimpleCharacterConfigs()
	if characters != nil && len(characters) > 0 {
		t.Logf("Found %d simple character configs", len(characters))
		for _, char := range characters {
			t.Logf("Character: ID=%d, Name=%s", char.CharacterID, char.Name)
		}
	} else {
		t.Log("No simple character configs found")
	}
}
