package dbConnect

import (
	"context"
	"copyTheGame/src/config"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DatabaseConnect struct {
	uri        string
	dbName     string
	Client     *mongo.Client
	Db         *mongo.Database
	Ctx        context.Context
	cancelFunc context.CancelFunc
	connectDb  func(*DatabaseConnect)
}
var db DatabaseConnect 

func init() {
	db.uri = config.GetConfig("uri")
	db.dbName = config.GetConfig("dbName")
	db.connectDb = connectDb
}
func connectDb(db *DatabaseConnect) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	db.Ctx = ctx
	db.cancelFunc = cancel

	fullURI := fmt.Sprintf("%s/%s", db.uri, db.dbName)
	clientOptions := options.Client().ApplyURI(fullURI)
   
	client ,err:=mongo.Connect(ctx, clientOptions)
	if err!= nil {
		fmt.Println("Error connecting to database: ", err)
	}
	err =  client.Ping(ctx, nil)
	if err!= nil {
		fmt.Println("Error pinging database: ", err)
	}
	fmt.Println("Connected to database: ", fullURI)
	db.Client = client
	db.Db = client.Database(db.dbName)
}

func GetDb() *DatabaseConnect {
	if db.Client == nil {
		db.connectDb = connectDb
		db.connectDb(&db)
	}
	return &db
}

func Client() *mongo.Client {
	return db.Client
}
