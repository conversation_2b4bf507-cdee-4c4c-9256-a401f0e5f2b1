package main

import (
	"fmt"
	"net/http"

	"copyTheGame/src/dbCon"
	"copyTheGame/src/modules/user"
	"copyTheGame/src/events"
	
	"github.com/labstack/echo/v4"
)

func main() {
	var db = dbConnect.GetDb()

	user.CreateUser("<PERSON>","123456")
	fmt.Println("Database connection successful",db)

    // 创建一个Echo实例
    e := echo.New()

	var events = events.GetEvent()
	for path, handler := range events {
		var error = e.POST(path, handler)
		if error != nil {
			fmt.Println(error)
		}
	}

	e.POST("/hello", helloHandler)

    // 启动HTTP服务器
    fmt.Println("Starting server at port 8080")
    e.Logger.Fatal(e.Start(":8080"))
	
}

// 处理/hello路由的函数
func helloHandler(c echo.Context) error {
    return c.String(http.StatusOK, "Hello, World!")
}

