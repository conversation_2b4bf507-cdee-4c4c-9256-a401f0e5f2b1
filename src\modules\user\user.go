package user

import (
	"copyTheGame/src/dbCon"
	"fmt"
	"log"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)


type User struct {
	UserName string `bson:"userName"`
	Password string	`bson:"password"`
	Id primitive.ObjectID `bson:"_id"`
}

func getCollection() (*dbConnect.DatabaseConnect,*mongo.Collection) {
	db := dbConnect.GetDb()
	return db,db.Db.Collection("users")
}

func FindUserById(id string) User {
	db,collection := getCollection()
	var user User
	err:= collection.FindOne(db.Ctx, bson.M{"_id": id}).Decode(&user)
	if err != nil {
		log.Fatal(err)
	}
	return user
}

func FindUserByName(userName string) (User,error) {
	db,collection := getCollection()
	var user User
	err:= collection.FindOne(db.Ctx, bson.M{"userName": userName}).Decode(&user)
	return user,err
}

func CreateUser(userName string, password string) (User, error) {
	db,collection := getCollection()
    // 创建用户对象
    user := User{
        UserName: userName,
        Password: password,
        Id:       primitive.NewObjectID(), // 添加唯一ID
    }

    // 插入数据
	fmt.Println(user)
    insertResult,err := collection.InsertOne(db.Ctx, user)
	fmt.Printf("Inserted user with ID: %v\n", insertResult.InsertedID)
    return user,err
}
