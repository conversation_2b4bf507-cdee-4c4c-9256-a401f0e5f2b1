package user

import (
	"copyTheGame/src/events"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
)

func init() {
	events.InitEvents("Login",Login)
	events.InitEvents("CreateUser",Create)
}

type CreateUserReq struct {
	UserName string
	Password string
}

func Login(request echo.Context) error {
	var user User
	request.Bind(&user)
	userToLogin,err:= FindUserByName(user.UserName)
	if err != nil {
		return request.JSON(http.StatusNotFound, "User not found");
	}
	if userToLogin.Password == user.Password {
		fmt.Println("Login success")
		return request.JSON(http.StatusOK, userToLogin);
	} else {
		return request.JSON(http.StatusFailedDependency, "Invalid password");
	}
}

func Create(request echo.Context) error {
	var user CreateUserReq
	request.Bind(&user)
	err := error(nil)
	var CreateRes User
	CreateRes,err = CreateUser(user.UserName, user.Password)
	if err!= nil {
		return request.JSON(http.StatusConflict, "User already exists");
	}
	return request.JSON(http.StatusOK, CreateRes)
}

