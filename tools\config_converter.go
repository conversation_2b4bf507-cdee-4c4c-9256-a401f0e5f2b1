package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"copyTheGame/src/config"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]
	
	switch command {
	case "excel-to-proto":
		convertExcelToProto()
	case "show-config":
		showCurrentConfig()
	case "validate-excel":
		validateExcelConfig()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("配置转换工具")
	fmt.Println("用法:")
	fmt.Println("  go run tools/config_converter.go <command>")
	fmt.Println("")
	fmt.Println("命令:")
	fmt.Println("  excel-to-proto  - 将 Excel 配置转换为 Proto 二进制文件")
	fmt.Println("  show-config     - 显示当前加载的配置")
	fmt.Println("  validate-excel  - 验证 Excel 配置文件格式")
}

func convertExcelToProto() {
	fmt.Println("生成示例 JSON 配置文件...")

	// 获取当前的简化配置
	gameConfig := config.GetSimpleGameConfig()
	if gameConfig == nil {
		fmt.Println("无法获取游戏配置")
		return
	}

	// 将配置保存为 JSON 文件
	data, err := json.MarshalIndent(gameConfig, "", "  ")
	if err != nil {
		log.Fatalf("序列化配置失败: %v", err)
	}

	err = os.WriteFile("./config/game_config.json", data, 0644)
	if err != nil {
		log.Fatalf("保存配置文件失败: %v", err)
	}

	fmt.Println("配置文件生成完成! 输出文件: ./config/game_config.json")
}

func showCurrentConfig() {
	fmt.Println("当前配置信息:")
	fmt.Println("================")

	// 获取简化配置
	gameConfig := config.GetSimpleGameConfig()
	if gameConfig == nil {
		fmt.Println("无法获取游戏配置")
		return
	}

	// 显示数据库配置
	if gameConfig.Database != nil {
		fmt.Println("数据库配置:")
		fmt.Printf("  URI: %s\n", gameConfig.Database.URI)
		fmt.Printf("  数据库名: %s\n", gameConfig.Database.DBName)
		fmt.Printf("  连接超时: %d秒\n", gameConfig.Database.ConnectionTimeout)
		fmt.Printf("  最大连接池: %d\n", gameConfig.Database.MaxPoolSize)
		fmt.Printf("  最小连接池: %d\n", gameConfig.Database.MinPoolSize)
	} else {
		fmt.Println("数据库配置: 未找到")
	}

	fmt.Println()

	// 显示服务器配置
	if gameConfig.Server != nil {
		fmt.Println("服务器配置:")
		fmt.Printf("  主机: %s\n", gameConfig.Server.Host)
		fmt.Printf("  端口: %d\n", gameConfig.Server.Port)
		fmt.Printf("  调试模式: %t\n", gameConfig.Server.DebugMode)
		fmt.Printf("  最大连接数: %d\n", gameConfig.Server.MaxConnections)
		fmt.Printf("  读取超时: %d秒\n", gameConfig.Server.ReadTimeout)
		fmt.Printf("  写入超时: %d秒\n", gameConfig.Server.WriteTimeout)
	} else {
		fmt.Println("服务器配置: 未找到")
	}

	fmt.Println()

	// 显示游戏配置
	if gameConfig.Game != nil {
		fmt.Println("游戏配置:")
		fmt.Printf("  游戏名: %s\n", gameConfig.Game.GameName)
		fmt.Printf("  版本: %s\n", gameConfig.Game.Version)
		fmt.Printf("  最大玩家数: %d\n", gameConfig.Game.MaxPlayers)
		fmt.Printf("  等级上限: %d\n", gameConfig.Game.LevelCap)
		fmt.Printf("  经验倍率: %.2f\n", gameConfig.Game.ExpMultiplier)
		fmt.Printf("  金币倍率: %d\n", gameConfig.Game.GoldMultiplier)
	} else {
		fmt.Println("游戏配置: 未找到")
	}

	fmt.Println()

	// 显示角色配置
	if gameConfig.Characters != nil && len(gameConfig.Characters) > 0 {
		fmt.Printf("角色配置 (共 %d 个):\n", len(gameConfig.Characters))
		for _, char := range gameConfig.Characters {
			fmt.Printf("  ID: %d, 名称: %s\n", char.CharacterID, char.Name)
			fmt.Printf("    描述: %s\n", char.Description)
			fmt.Printf("    基础属性: HP=%d, MP=%d, 攻击=%d, 防御=%d, 速度=%d\n",
				char.BaseHP, char.BaseMP, char.BaseAttack, char.BaseDefense, char.BaseSpeed)
			if len(char.SkillIDs) > 0 {
				fmt.Printf("    技能ID: %v\n", char.SkillIDs)
			}
			fmt.Println()
		}
	} else {
		fmt.Println("角色配置: 未找到")
	}
}

func validateExcelConfig() {
	fmt.Println("验证 JSON 配置文件...")

	// 尝试加载 JSON 配置文件
	data, err := os.ReadFile("./config/game_config.json")
	if err != nil {
		fmt.Printf("❌ JSON 配置文件读取失败: %v\n", err)
		return
	}

	var gameConfig config.SimpleGameConfig
	err = json.Unmarshal(data, &gameConfig)
	if err != nil {
		fmt.Printf("❌ JSON 配置文件解析失败: %v\n", err)
		return
	}

	fmt.Println("✅ JSON 配置文件格式正确!")

	// 基本验证
	errors := []string{}

	if gameConfig.Database == nil {
		errors = append(errors, "缺少数据库配置")
	} else {
		if gameConfig.Database.URI == "" {
			errors = append(errors, "数据库 URI 不能为空")
		}
		if gameConfig.Database.DBName == "" {
			errors = append(errors, "数据库名称不能为空")
		}
	}

	if gameConfig.Server == nil {
		errors = append(errors, "缺少服务器配置")
	} else {
		if gameConfig.Server.Port <= 0 {
			errors = append(errors, "服务器端口必须大于 0")
		}
	}

	if gameConfig.Game == nil {
		errors = append(errors, "缺少游戏配置")
	} else {
		if gameConfig.Game.GameName == "" {
			errors = append(errors, "游戏名称不能为空")
		}
		if gameConfig.Game.Version == "" {
			errors = append(errors, "游戏版本不能为空")
		}
	}

	if len(errors) > 0 {
		fmt.Println("⚠️  发现以下问题:")
		for _, err := range errors {
			fmt.Printf("  - %s\n", err)
		}
	} else {
		fmt.Println("✅ 配置内容验证通过!")
	}
}
