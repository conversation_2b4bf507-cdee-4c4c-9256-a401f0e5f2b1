package main

import (
	"fmt"
	"log"
	"os"

	"copyTheGame/src/config"
)

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]
	
	switch command {
	case "excel-to-proto":
		convertExcelToProto()
	case "show-config":
		showCurrentConfig()
	case "validate-excel":
		validateExcelConfig()
	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("配置转换工具")
	fmt.Println("用法:")
	fmt.Println("  go run tools/config_converter.go <command>")
	fmt.Println("")
	fmt.Println("命令:")
	fmt.Println("  excel-to-proto  - 将 Excel 配置转换为 Proto 二进制文件")
	fmt.Println("  show-config     - 显示当前加载的配置")
	fmt.Println("  validate-excel  - 验证 Excel 配置文件格式")
}

func convertExcelToProto() {
	fmt.Println("开始转换 Excel 配置到 Proto 二进制文件...")
	
	excelMaker := config.NewExcelMaker("./config/game_config.xlsx")
	
	// 加载 Excel 配置
	gameConfig, err := excelMaker.LoadGameConfiguration()
	if err != nil {
		log.Fatalf("加载 Excel 配置失败: %v", err)
	}
	
	fmt.Println("Excel 配置加载成功!")
	
	// 保存为 Proto 二进制文件
	err = excelMaker.SaveToProtoFile(gameConfig, "./config/game_config.pb")
	if err != nil {
		log.Fatalf("保存 Proto 文件失败: %v", err)
	}
	
	fmt.Println("配置转换完成! 输出文件: ./config/game_config.pb")
}

func showCurrentConfig() {
	fmt.Println("当前配置信息:")
	fmt.Println("================")
	
	// 显示数据库配置
	if dbConfig := config.GetDatabaseConfig(); dbConfig != nil {
		fmt.Println("数据库配置 (Proto):")
		fmt.Printf("  URI: %s\n", dbConfig.Uri)
		fmt.Printf("  数据库名: %s\n", dbConfig.DbName)
		fmt.Printf("  连接超时: %d秒\n", dbConfig.ConnectionTimeout)
		fmt.Printf("  最大连接池: %d\n", dbConfig.MaxPoolSize)
		fmt.Printf("  最小连接池: %d\n", dbConfig.MinPoolSize)
	} else {
		fmt.Println("数据库配置 (JSON 后备):")
		fmt.Printf("  URI: %s\n", config.GetConfig("uri"))
		fmt.Printf("  数据库名: %s\n", config.GetConfig("dbName"))
	}
	
	fmt.Println()
	
	// 显示服务器配置
	if serverConfig := config.GetServerConfig(); serverConfig != nil {
		fmt.Println("服务器配置 (Proto):")
		fmt.Printf("  主机: %s\n", serverConfig.Host)
		fmt.Printf("  端口: %d\n", serverConfig.Port)
		fmt.Printf("  调试模式: %t\n", serverConfig.DebugMode)
		fmt.Printf("  最大连接数: %d\n", serverConfig.MaxConnections)
		fmt.Printf("  读取超时: %d秒\n", serverConfig.ReadTimeout)
		fmt.Printf("  写入超时: %d秒\n", serverConfig.WriteTimeout)
	} else {
		fmt.Println("服务器配置: 未找到")
	}
	
	fmt.Println()
	
	// 显示游戏配置
	if gameConfig := config.GetGameBasicConfig(); gameConfig != nil {
		fmt.Println("游戏配置 (Proto):")
		fmt.Printf("  游戏名: %s\n", gameConfig.GameName)
		fmt.Printf("  版本: %s\n", gameConfig.Version)
		fmt.Printf("  最大玩家数: %d\n", gameConfig.MaxPlayers)
		fmt.Printf("  等级上限: %d\n", gameConfig.LevelCap)
		fmt.Printf("  经验倍率: %.2f\n", gameConfig.ExpMultiplier)
		fmt.Printf("  金币倍率: %d\n", gameConfig.GoldMultiplier)
	} else {
		fmt.Println("游戏配置: 未找到")
	}
	
	fmt.Println()
	
	// 显示角色配置
	if characters := config.GetCharacterConfigs(); characters != nil && len(characters) > 0 {
		fmt.Printf("角色配置 (共 %d 个):\n", len(characters))
		for _, char := range characters {
			fmt.Printf("  ID: %d, 名称: %s\n", char.CharacterId, char.Name)
			fmt.Printf("    描述: %s\n", char.Description)
			fmt.Printf("    基础属性: HP=%d, MP=%d, 攻击=%d, 防御=%d, 速度=%d\n", 
				char.BaseHp, char.BaseMp, char.BaseAttack, char.BaseDefense, char.BaseSpeed)
			if len(char.SkillIds) > 0 {
				fmt.Printf("    技能ID: %v\n", char.SkillIds)
			}
			fmt.Println()
		}
	} else {
		fmt.Println("角色配置: 未找到")
	}
}

func validateExcelConfig() {
	fmt.Println("验证 Excel 配置文件...")
	
	excelMaker := config.NewExcelMaker("./config/game_config.xlsx")
	
	// 尝试加载配置
	gameConfig, err := excelMaker.LoadGameConfiguration()
	if err != nil {
		fmt.Printf("❌ Excel 配置验证失败: %v\n", err)
		return
	}
	
	fmt.Println("✅ Excel 配置文件格式正确!")
	
	// 基本验证
	errors := []string{}
	
	if gameConfig.Database == nil {
		errors = append(errors, "缺少数据库配置")
	} else {
		if gameConfig.Database.Uri == "" {
			errors = append(errors, "数据库 URI 不能为空")
		}
		if gameConfig.Database.DbName == "" {
			errors = append(errors, "数据库名称不能为空")
		}
	}
	
	if gameConfig.Server == nil {
		errors = append(errors, "缺少服务器配置")
	} else {
		if gameConfig.Server.Port <= 0 {
			errors = append(errors, "服务器端口必须大于 0")
		}
	}
	
	if gameConfig.Game == nil {
		errors = append(errors, "缺少游戏配置")
	} else {
		if gameConfig.Game.GameName == "" {
			errors = append(errors, "游戏名称不能为空")
		}
		if gameConfig.Game.Version == "" {
			errors = append(errors, "游戏版本不能为空")
		}
	}
	
	if len(errors) > 0 {
		fmt.Println("⚠️  发现以下问题:")
		for _, err := range errors {
			fmt.Printf("  - %s\n", err)
		}
	} else {
		fmt.Println("✅ 配置内容验证通过!")
	}
}
